import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getTableList } from '@app/action/tableList';
import { Table } from '@components/common';
import OrderColumn from '@components/common/orderColumn'; // ✅ 导入正确的排序组件
import { getCrumb } from '@utils/utils';
import { searchApi, opApi } from '@app/api'; // 🔄 添加 opApi 用于群聊管理
import {
  Button,
  Divider,
  Col,
  Form,
  Icon,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
} from 'antd';
import { useRouteMatch } from 'react-router-dom';
import { CommonObject } from '@app/types';
import { setMenuHook } from '@app/utils/utils';
import CreateGroupChat from './components/CreateGroupChat';

interface FilterState {
  circle_id: string;
  search_type: number;
  keyword: string;
}



export default function GroupChatMgr(props: any) {
  const dispatch = useDispatch();
  const match = useRouteMatch();

  // Redux state
  const tableList = useSelector((state: any) => state.tableList);
  const tableCache = useSelector((state: any) => state.tableCache);
  const { current, size, records = [] } = useSelector((state: any) => state.tableList);

  // Local state
  const [filter, setFilter] = useState<FilterState>({
    circle_id: '',
    search_type: 1, // 1: 群名称, 2: 群ID
    keyword: '',
  });

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // 搜索状态
  const [search, setSearch] = useState<{
    keyword: string;
  }>({
    keyword: '',
  });

  // 编辑群聊弹窗
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editRecord, setEditRecord] = useState<any>({});

  // 排序弹窗已改为 Modal.confirm 实现，不再需要状态管理

  // 创建群聊弹窗
  const [createModalVisible, setCreateModalVisible] = useState(false);

  // 圈子列表
  const [circleList, setCircleList] = useState<any[]>([]);

  // 初始化
  useEffect(() => {
    setMenuHook(dispatch, props);
    loadCircleList();

    if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
      getData({ current: tableCache.current, size: tableCache.size });
    } else {
      getData({ current: 1 });
    }

    setIsInitialized(true);
  }, []);

  // 加载圈子列表
  const loadCircleList = () => {
    searchApi
      .getCircleList({ current: 1, size: 100, enabled: true })
      .then((res) => {
        console.log(res)
        const { list = [] } = res.data as any;
        setCircleList(list.records);
      })
      .catch(() => {});
  };

  // 获取数据
  const getData = (overlap: CommonObject = {}) => {
    const params = { ...getFilter(), ...overlap };
    console.log('获取群聊列表数据', params);
    // 🔄 使用真实API获取群聊列表数据
    dispatch(getTableList('getGroupChatList', 'list', params));
  };

  // 获取过滤条件
  const getFilter = () => {
    const { current, size } = tableList;
    const filters: CommonObject = { current, size };

    // 添加所有筛选字段
    Object.keys(filter).forEach((key) => {
      const value = filter[key as keyof typeof filter];
      if (value !== '' && value !== undefined) {
        filters[key] = value;
      }
    });

    return filters;
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    if (key === 'search_type') {
      // 搜索类型变化时，清空关键词
      setFilter({
        ...filter,
        [key]: value,
        keyword: '',
      });
      setSearch({
        keyword: '',
      });
    } else {
      setFilter({
        ...filter,
        [key]: value,
      });
    }
  };

  // 监听 filter 变化
  useEffect(() => {
    if (isInitialized) {
      getData({ current: 1 });
    }
  }, [filter]);

  // 处理搜索
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setFilter({
      ...filter,
      keyword: search.keyword,
    });
  };

  // 创建群聊
  const handleCreateGroup = () => {
    setCreateModalVisible(true);
  };

  // 处理创建群聊成功
  const handleCreateGroupSuccess = (values: any) => {
    console.log('创建群聊成功', values);
    // 🐛 修复：CreateGroupChat 组件内部已经处理了 API 调用
    // 这里只需要刷新列表数据，不需要重复调用 API
    getData();
    setCreateModalVisible(false);
  };

  // 处理创建群聊取消
  const handleCreateGroupCancel = () => {
    setCreateModalVisible(false);
  };



  // 编辑群聊信息
  const handleEdit = (record: any) => {
    console.log('编辑群聊信息', record);
    setEditRecord(record);
    setEditModalVisible(true);
  };

  // 排序操作 - 🔄 实现排序弹窗功能
  const handleSortAction = (record: any) => {
    console.log('排序操作', record);

    // 获取当前记录在列表中的位置
    const currentIndex = records.findIndex((r: any) => r.id === record.id);
    const currentPosition = currentIndex + 1;

    let newPosition = currentPosition;
    const positionChange = (value: number | undefined) => {
      newPosition = value || currentPosition;
    };

    Modal.confirm({
      title: <p>排序：{record.group_name}</p>,
      icon: <Icon type="info-circle" />,
      content: (
        <div>
          <span>请输入位置：</span>
          <InputNumber
            min={1}
            max={records.length}
            defaultValue={currentPosition}
            onChange={positionChange}
          />
        </div>
      ),
      onOk: () => {
        if (!newPosition || newPosition < 1 || newPosition > records.length) {
          message.error('请填写有效的位置');
          return Promise.reject();
        }

        if (newPosition === currentPosition) {
          message.info('位置未发生变化');
          return Promise.resolve();
        }

        // 🔄 调用排序API - 根据接口文档更新参数
        return opApi.sortGroupChat({
          id: record.id || '',
          circle_id: filter.circle_id || '', // ✅ 添加圈子ID参数
          sort_flag: 2, // 2-指定位置
          position: newPosition || 0,
        }).then(() => {
          message.success('排序成功');
          getData();
        }).catch(() => {
          message.error('排序失败');
          return Promise.reject();
        });
      },
      okText: '确定',
      cancelText: '取消',
    });
  };

  // 删除群聊
  const handleDelete = (record: any) => {
    Modal.confirm({
      title: '删除后群聊将被解散，不可恢复',
      content: '',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        console.log('删除群聊', record);
        // 🔄 根据接口文档调用删除API
        return opApi.deleteGroupChat({
          id: record.id || '',
        }).then(() => {
          message.success('删除成功');
          getData(); // 刷新列表
        }).catch(() => {
          message.error('删除失败');
          return Promise.reject();
        });
      },
    });
  };

  // 复制链接
  const handleCopyLink = (record: any) => {
    console.log('复制群分享链接', record);
    // 🔄 使用接口文档中的 url 字段
    const shareUrl = record.url || `https://example.com/group/${record.im_id}`;

    // ✅ 复制群分享链接逻辑预留
    if (navigator.clipboard) {
      navigator.clipboard.writeText(shareUrl).then(() => {
        message.success('链接已复制');
      }).catch(() => {
        message.error('复制失败');
      });
    } else {
      // 兼容旧浏览器
      const textArea = document.createElement('textarea');
      textArea.value = shareUrl;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      message.success('链接已复制');
    }
  };

  // 上移
  const handleMoveUp = (record: any) => {
    console.log('上移', record);
    // 🔄 根据接口文档实现上移逻辑
    opApi.sortGroupChat({
      id: record.id || '',
      circle_id: filter.circle_id || '', // ✅ 添加圈子ID参数
      sort_flag: 0, // 0-向上
    }).then(() => {
      message.success('排序成功');
      getData();
    }).catch(() => {
      message.error('排序失败');
    });
  };

  // 下移
  const handleMoveDown = (record: any) => {
    console.log('下移', record);
    // 🔄 根据接口文档实现下移逻辑
    opApi.sortGroupChat({
      id: record.id || '',
      circle_id: filter.circle_id || '', // ✅ 添加圈子ID参数
      sort_flag: 1, // 1-向下
    }).then(() => {
      message.success('排序成功');
      getData();
    }).catch(() => {
      message.error('排序失败');
    });
  };

  // 处理批量选择
  const handleSelectChange = (selectedKeys: any[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // 获取序号
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  // 是否显示排序功能（选择圈子后显示）
  const showSortFeature = filter.circle_id !== '';

  // 获取列配置
  const columns = [
    {
      title: '排序',
      key: 'sort',
      width: 80,
      render: (_: any, record: any, i: number) => {
        if (!showSortFeature) return null;

        return (
          <OrderColumn
            perm="" // ✅ 权限控制
            start={1}
            pos={getSeq(i)}
            end={records.length}
            onUp={() => handleMoveUp(record)}
            onDown={() => handleMoveDown(record)}
          />
        );
      },
    },
    {
      title: '序号',
      key: 'seq',
      render: (_: any, __: any, i: number) => <span>{getSeq(i)}</span>,
      width: 60,
    },
    {
      title: '群ID',
      dataIndex: 'im_id', // 🔄 更新字段映射
      key: 'im_id',
      width: 100,
    },
    {
      title: '群名称',
      dataIndex: 'group_name',
      key: 'group_name',
      width: 200,
      render: (text: string, record: any) => (
        <a onClick={() => handleEdit(record)}>{text}</a>
      ),
    },
    {
      title: '群成员数',
      dataIndex: 'member_count',
      key: 'member_count',
      width: 100,
    },
    {
      title: '群主',
      dataIndex: 'owner_account_name', // 🔄 更新字段映射
      key: 'owner_account_name',
      width: 150,
    },
    {
      title: '关联圈子',
      key: 'circle_name',
       dataIndex: 'circle_name',
      width: 120,
    },
    {
      title: '操作人',
      dataIndex: 'created_by', // 🔄 更新字段映射
      key: 'created_by',
      width: 100,
    },
    {
      title: '最后操作时间',
      dataIndex: 'created_at', // 🔄 更新字段映射
      key: 'created_at',
      width: 160,
      render: (timestamp: number) => {
        // 🔄 处理时间戳格式化
        if (!timestamp) return '-';
        return new Date(timestamp).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
      },
    },
    {
      title: '操作',
      key: 'op',
      width: 200,
      fixed: 'right',
      render: (_: any, record: any) => (
        <span>
          <a onClick={() => handleEdit(record)}>编辑</a>
          {showSortFeature && (
            <>
              <Divider type="vertical" />
              <a onClick={() => handleSortAction(record)}>排序</a>
            </>
          )}
          <Divider type="vertical" />
          <a onClick={() => handleDelete(record)}>删除</a>
          <Divider type="vertical" />
          <a onClick={() => handleCopyLink(record)}>复制链接</a>
        </span>
      ),
    },
  ].filter(col => {
    // 未选择圈子时隐藏排序列
    if (col.key === 'sort' && !showSortFeature) {
      return false;
    }
    return true;
  });

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button
            onClick={handleCreateGroup}
          >
             创建群聊
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>

      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Form layout="inline">
              <Form.Item>
                <Select
                  value={filter.circle_id}
                  onChange={(value) => handleFilterChange('circle_id', value)}
                  style={{ width: 150 }}
                  placeholder="全部圈子"
                >
                  <Select.Option value="">全部圈子</Select.Option>
                  {circleList.map((circle) => (
                    <Select.Option key={circle.id} value={circle.id}>
                      {circle.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Select
                value={filter.search_type}
                onChange={(value) => handleFilterChange('search_type', value)}
                style={{ width: 100, marginRight: 8 }}
              >
                <Select.Option value={1}>群名称</Select.Option>
                <Select.Option value={2}>群ID</Select.Option>
              </Select>
              <Input
                value={search.keyword}
                onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
                placeholder="请输入群名称或群ID"
                style={{ width: 200, marginRight: 8 }}
                onKeyDown={handleKeyDown}
              />
              <Button type="primary" onClick={handleSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          func="getGroupChatList"
          index="list"
          rowKey="id"
          filter={getFilter()}
          columns={columns}
          pagination={true}
          multi={false}
          selectedRowKeys={selectedRowKeys}
          onSelectChange={handleSelectChange}
          tableProps={{ scroll: { x: 1200 } }}
        />

        {/* 创建群聊弹窗 */}
        <CreateGroupChat
          visible={createModalVisible}
          onCancel={handleCreateGroupCancel}
          onOk={handleCreateGroupSuccess}
          circleList={circleList}
        />

        {/* 编辑群聊弹窗 - ✅ 使用统一的 CreateGroupChat 组件 */}
        <CreateGroupChat
          visible={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          onOk={(values) => {
            console.log('编辑群聊成功', values);
            // 🐛 修复：CreateGroupChat 组件内部已经处理了 API 调用
            // 这里只需要刷新列表数据和关闭弹窗
            setEditModalVisible(false);
            getData(); // 刷新列表
          }}
          editData={editRecord} // ✅ 传递编辑数据
          circleList={circleList}
        />
      </div>
    </>
  );
}